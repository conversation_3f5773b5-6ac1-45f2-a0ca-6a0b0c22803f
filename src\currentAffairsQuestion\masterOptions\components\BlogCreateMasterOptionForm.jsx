import React, { useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { Form, Button, Container, Card } from 'react-bootstrap';
import { useDispatch, useSelector } from 'react-redux';
import { createMasterOption } from '../../../redux/slice/masterOptionSlice';
import RichTextEditor from '../../../CommonComponents/RichTextEditor'; // Adjust the slice import as necessary

export default function BlogCreateMasterOptionForm({ onMasterOptionContentChange, onMasterOptionCreated }) {
  const dispatch = useDispatch();
  const contributorProfileId = useSelector((state) => state.contributor.contributorProfileId || null);
  const { blogSlug } = useParams();

  const { isLoading, error } = useSelector((state) => state.masterOption); // Adjust for masterOption state

  // State for form fields
  const [title, setTitle] = useState('');
  const [optionContent, setOptionContent] = useState('');
  const [conditions, setConditions] = useState('');

  // Form submit handler
  const handleSubmit = (e) => {
    e.preventDefault();

    // Form data structure
    const masterOptionData = {
      author: contributorProfileId,
      title,
      option_content: optionContent,
      conditions,
      current_affairs: blogSlug, // Passing directly
      is_current_affairs: true, // Passing directly
    };

    // Dispatch the createMasterOption thunk
    dispatch(createMasterOption(masterOptionData))
      .then(() => {
        // After successful creation, trigger any necessary updates
        if (onMasterOptionCreated) {
          onMasterOptionCreated(); // You can trigger the option update or refresh
        }
        // Reset form fields and update the search term to the new title
        setTitle('');
        setOptionContent('');
        setConditions('');
        onMasterOptionContentChange('');  // Sync title with search term
      });
  };

  return (
    <Container className="mt-4">
      <Card className="shadow-lg p-4">
        <Form onSubmit={handleSubmit}>
          {/* Title Input */}
          <Form.Group controlId="formTitle" className="mb-3">
            <RichTextEditor
              label="Title"
              name="title"
              value={title}
              onChange={(e) => {
                setTitle(e.target.value);
                onMasterOptionContentChange(e.target.value);  // Update search term as title changes
              }}
              placeholder="Enter title"
              rows={2}
              required
            />
          </Form.Group>

          {/* Option Content Textarea */}
          <Form.Group controlId="formOptionContent" className="mb-3">
            <RichTextEditor
              label="Option Content"
              name="optionContent"
              value={optionContent}
              onChange={(e) => setOptionContent(e.target.value)}
              placeholder="Enter option content"
              rows={5}
              required
            />
          </Form.Group>

          {/* Conditions Textarea */}
          <Form.Group controlId="formConditions" className="mb-3">
            <RichTextEditor
              label="Conditions"
              name="conditions"
              value={conditions}
              onChange={(e) => setConditions(e.target.value)}
              placeholder="Enter conditions for displaying this option"
              rows={3}
              required
            />
          </Form.Group>

          {/* Submit Button */}
          <div className="d-flex justify-content-center mt-3">
            <Button type="submit" variant="outline-success" disabled={isLoading}>
              {isLoading ? 'Submitting...' : 'Create Master Option'}
            </Button>
          </div>

          {/* Error Message */}
          {error && (
            <div className="text-danger mt-3">
              <strong>Error:</strong> {error}
            </div>
          )}
        </Form>
      </Card>
    </Container>
  );
}
