import React, { useState } from 'react';
import { Form, Button, Container, Card } from 'react-bootstrap';
import { useDispatch, useSelector } from 'react-redux';
import { createMasterQuestion } from '../../redux/slice/masterQuestionSlice';
import RichTextEditor from '../../CommonComponents/RichTextEditor';


export default function CreateMasterQuestionForm({ onMasterQuestionContentChange, onMasterQuestionCreated }) {
  const dispatch = useDispatch();
  const contributorProfileId = useSelector((state) => state.contributor.contributorProfileId || null);
  
  const { isLoading, error } = useSelector((state) => state.masterQuestion);

  // State for form fields
  const [title, setTitle] = useState('');
  const [passageContent, setPassageContent] = useState('');

  // Form submit handler
  const handleSubmit = (e) => {
    e.preventDefault();

    // Form data structure
    const masterQuestionData = {
      author: contributorProfileId,
      title,
      passage_content: passageContent,
      current_affairs: null,
      is_current_affairs: false,
    };

    // Dispatch the createMasterQuestion thunk
    dispatch(createMasterQuestion(masterQuestionData))
      .then(() => {
        // After successful creation, dispatch getAllMasterQuestions
        if (onMasterQuestionCreated) {
          onMasterQuestionCreated(); // Fetch all master questions
        }
        // Reset form fields and update the search term to the new title
        setTitle('');
        setPassageContent('');
        onSearchTermChange('');  // Sync title with search term
      });
  };

  return (
    <Container className="mt-4">
      <Card className="shadow-lg p-4">
        <Form onSubmit={handleSubmit}>
          {/* Title Input */}
          <Form.Group controlId="formTitle" className="mb-3">
            <RichTextEditor
              label="Title"
              name="title"
              value={title}
              onChange={(e) => {
                setTitle(e.target.value);
                onMasterQuestionContentChange(e.target.value);  // Update search term as title changes
              }}
              placeholder="Enter title"
              rows={2}
              required
            />
          </Form.Group>

          {/* Passage Content Textarea */}
          <Form.Group controlId="formPassageContent" className="mb-3">
            <RichTextEditor
              label="Passage Content"
              name="passageContent"
              value={passageContent}
              onChange={(e) => setPassageContent(e.target.value)}
              placeholder="Enter passage content"
              rows={5}
              required
            />
          </Form.Group>

          {/* Submit Button */}
          <div className="d-flex justify-content-center mt-3">
            <Button type="submit" variant="outline-success" disabled={isLoading}>
              {isLoading ? 'Submitting...' : 'Create Master Question'}
            </Button>
          </div>

          {/* Error Message */}
          {error && (
            <div className="text-danger mt-3">
              <strong>Error:</strong> {error}
            </div>
          )}
        </Form>
      </Card>
    </Container>
  );
}
