import React, { useState, useRef, useEffect } from 'react';
import { Form, Button, ButtonGroup, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { FaBold, FaItalic, FaUnderline, FaPalette } from 'react-icons/fa';

const RichTextEditor = ({
  value = '',
  onChange,
  placeholder = 'Enter text...',
  rows = 3,
  label,
  required = false,
  className = '',
  name = '',
  disabled = false
}) => {
  const textareaRef = useRef(null);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [selectedColor, setSelectedColor] = useState('#000000');

  // Common colors for quick selection
  const commonColors = [
    '#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', 
    '#FF00FF', '#00FFFF', '#FFA500', '#800080', '#008000',
    '#800000', '#000080', '#808080', '#C0C0C0', '#808000'
  ];

  // Get current selection in textarea
  const getCurrentSelection = () => {
    const textarea = textareaRef.current;
    if (!textarea) return { start: 0, end: 0, text: '' };
    
    return {
      start: textarea.selectionStart,
      end: textarea.selectionEnd,
      text: textarea.value.substring(textarea.selectionStart, textarea.selectionEnd)
    };
  };

  // Insert formatting tags around selected text
  const insertFormatting = (startTag, endTag) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const { start, end, text } = getCurrentSelection();
    const beforeText = value.substring(0, start);
    const afterText = value.substring(end);
    
    let newText;
    if (text) {
      // If text is selected, wrap it with tags
      newText = beforeText + startTag + text + endTag + afterText;
    } else {
      // If no text selected, insert tags at cursor position
      newText = beforeText + startTag + endTag + afterText;
    }
    
    onChange({ target: { name, value: newText } });
    
    // Set cursor position after the start tag
    setTimeout(() => {
      const newCursorPos = start + startTag.length + (text ? text.length : 0);
      textarea.setSelectionRange(newCursorPos, newCursorPos);
      textarea.focus();
    }, 0);
  };

  // Handle bold formatting
  const handleBold = () => {
    insertFormatting('<b>', '</b>');
  };

  // Handle italic formatting
  const handleItalic = () => {
    insertFormatting('<i>', '</i>');
  };

  // Handle underline formatting
  const handleUnderline = () => {
    insertFormatting('<u>', '</u>');
  };

  // Handle color formatting
  const handleColor = (color) => {
    insertFormatting(`<span style="color: ${color}">`, '</span>');
    setShowColorPicker(false);
  };

  // Handle custom color input
  const handleCustomColor = () => {
    handleColor(selectedColor);
  };

  // Render formatted preview
  const renderPreview = (text) => {
    if (!text) return null;
    
    // Convert HTML tags to JSX-safe format for preview
    return (
      <div 
        dangerouslySetInnerHTML={{ 
          __html: text
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/&lt;b&gt;/g, '<b>')
            .replace(/&lt;\/b&gt;/g, '</b>')
            .replace(/&lt;i&gt;/g, '<i>')
            .replace(/&lt;\/i&gt;/g, '</i>')
            .replace(/&lt;u&gt;/g, '<u>')
            .replace(/&lt;\/u&gt;/g, '</u>')
            .replace(/&lt;span style="color: ([^"]+)"&gt;/g, '<span style="color: $1">')
            .replace(/&lt;\/span&gt;/g, '</span>')
        }} 
      />
    );
  };

  return (
    <div className={className}>
      {label && <Form.Label>{label}</Form.Label>}
      
      {/* Formatting Toolbar */}
      <div className="mb-2 d-flex align-items-center gap-2">
        <ButtonGroup size="sm">
          <OverlayTrigger
            placement="top"
            overlay={<Tooltip>Bold</Tooltip>}
          >
            <Button
              variant="outline-secondary"
              onClick={handleBold}
              disabled={disabled}
            >
              <FaBold />
            </Button>
          </OverlayTrigger>
          
          <OverlayTrigger
            placement="top"
            overlay={<Tooltip>Italic</Tooltip>}
          >
            <Button
              variant="outline-secondary"
              onClick={handleItalic}
              disabled={disabled}
            >
              <FaItalic />
            </Button>
          </OverlayTrigger>
          
          <OverlayTrigger
            placement="top"
            overlay={<Tooltip>Underline</Tooltip>}
          >
            <Button
              variant="outline-secondary"
              onClick={handleUnderline}
              disabled={disabled}
            >
              <FaUnderline />
            </Button>
          </OverlayTrigger>
        </ButtonGroup>

        {/* Color Picker */}
        <div className="position-relative">
          <OverlayTrigger
            placement="top"
            overlay={<Tooltip>Text Color</Tooltip>}
          >
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={() => setShowColorPicker(!showColorPicker)}
              disabled={disabled}
            >
              <FaPalette />
            </Button>
          </OverlayTrigger>
          
          {showColorPicker && (
            <div 
              className="position-absolute bg-white border rounded p-2 shadow-lg"
              style={{ 
                top: '100%', 
                left: 0, 
                zIndex: 1000,
                minWidth: '200px'
              }}
            >
              <div className="mb-2">
                <small className="text-muted">Quick Colors:</small>
                <div className="d-flex flex-wrap gap-1 mt-1">
                  {commonColors.map((color) => (
                    <button
                      key={color}
                      type="button"
                      className="btn p-0 border"
                      style={{
                        width: '20px',
                        height: '20px',
                        backgroundColor: color,
                        cursor: 'pointer'
                      }}
                      onClick={() => handleColor(color)}
                      title={color}
                    />
                  ))}
                </div>
              </div>
              
              <div className="d-flex gap-2 align-items-center">
                <input
                  type="color"
                  value={selectedColor}
                  onChange={(e) => setSelectedColor(e.target.value)}
                  className="form-control form-control-color"
                  style={{ width: '40px', height: '30px' }}
                />
                <Button
                  size="sm"
                  variant="primary"
                  onClick={handleCustomColor}
                >
                  Apply
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => setShowColorPicker(false)}
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Text Area */}
      <Form.Control
        ref={textareaRef}
        as="textarea"
        rows={rows}
        name={name}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        className="font-monospace"
        style={{ fontSize: '14px' }}
      />

      {/* Preview */}
      {value && (
        <div className="mt-2">
          <small className="text-muted">Preview:</small>
          <div 
            className="border rounded p-2 bg-light"
            style={{ minHeight: '40px' }}
          >
            {renderPreview(value)}
          </div>
        </div>
      )}

      {/* Help Text */}
      <small className="text-muted">
        Select text and use formatting buttons, or place cursor and click to insert tags.
      </small>
    </div>
  );
};

export default RichTextEditor;
