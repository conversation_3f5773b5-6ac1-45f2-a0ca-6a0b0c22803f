import React from "react";
import { Card, Col, Row } from "react-bootstrap";
import Skeleton from "react-loading-skeleton";

const BlogsCardsSkeleton = ({number, cardCol = 4}) => {
  return (
    <>
      <Row>
        {[...Array(number)].map(
          (
            _,
            index // Show 6 skeletons while loading
          ) => (
            <Col key={index} xs={12} sm={6} md={cardCol} className="mb-3">
              <Card className="mb-4 w-100">
                <Skeleton
                  height={160}
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
                <Card.Body>
                  <Skeleton
                    height={20}
                    width="80%"
                    baseColor="#e6ffe6"
                    highlightColor="#c4f7c4"
                  />
                  <Skeleton
                    height={15}
                    width="60%"
                    baseColor="#e6ffe6"
                    highlightColor="#c4f7c4"
                    className="my-2"
                  />
                  <Skeleton
                    height={50}
                    baseColor="#e6ffe6"
                    highlightColor="#c4f7c4"
                  />
                  <div className="d-flex justify-content-center mt-2">
                    <Skeleton
                      height={30}
                      width="30%"
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                    />
                    <Skeleton
                      height={30}
                      width="30%"
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                      className="mx-2"
                    />
                    <Skeleton
                      height={30}
                      width="30%"
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                    />
                  </div>
                  <div className="d-flex justify-content-center mt-2">
                    <Skeleton
                      height={30}
                      width="20%"
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                      className="mx-1"
                    />
                    <Skeleton
                      height={30}
                      width="20%"
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                      className="mx-1"
                    />
                    <Skeleton
                      height={30}
                      width="20%"
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                      className="mx-1"
                    />
                  </div>
                </Card.Body>
              </Card>
            </Col>
          )
        )}
      </Row>
    </>
  );
};

export default BlogsCardsSkeleton;
