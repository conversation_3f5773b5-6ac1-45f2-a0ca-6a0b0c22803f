import React, { useEffect, useState } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Toast, ToastContainer } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import Home from './lanndingPages/pages/Home';
import ContributorDashboard from "./lanndingPages/pages/ContributorDashboard";
import ContributorSignupPage from "./lanndingPages/pages/ContributorSignupPage";
import ContributorLogin from "./lanndingPages/pages/ContributorLogin";
import CoursesDashboard from './course/pages/CoursesDashboard';
import CourseForm from './course/pages/CourseForm';
import AllCourses from './course/components/AllCourses';
import AddCourses from './course/pages/AddCourse';
import ViewCourse from "./course/pages/ViewCourse";
import AddTestPattern from './testPattern/pages/AddTestPattern';
import ViewTestPatterns from "./testPattern/pages/ViewTestPatterns";
import EditTestPattern from "./testPattern/pages/EditTestPattern";
import QuestionsDashboard from './normalQuestions/pages/QuestionsDashboard';
import ViewQuestions from "./normalQuestions/components/ViewQuestions";
import CreateSubject from './subjects/pages/CreateSubject';
import ViewSubjects from './subjects/pages/ViewSubjects';
import ViewTopic from './subjects/pages/ViewTopics';
import MasterQuestionDashboard from './masterQuestions/pages/MasterQuestionDashboard';
import MasterOptionDashboard from './masterOptions/pages/MasterOptionDashboard';
import MasterQuestion from './masterQuestions/components/MasterQuestion';
import MasterOptionAddQuestionDashboard from './masterOptions/pages/MasterOptionAddQuestionDashboard';
import BlogsDashboard from './blogs/pages/BlogsDashboard';
import Blogs from './blogs/pages/Blogs';
import ViewBlog from './blogs/pages/ViewBlog';
import PreviousYearQuestionsDashboard from "./previousYearQuestions/pages/PreviousYearQuestionsDashboard";
import { Toaster, toast } from 'react-hot-toast';
import ContributionDashboard from './contribution/pages/ContributionDashboard';
import AddTier from './course/pages/AddTier';
import AddPaper from './course/pages/AddPaper';
import AddSection from './course/pages/AddSection';
import AddModule from './course/pages/AddModule';
import ShortMode from './blogs/components/ShortMode';
import Swal from 'sweetalert2'; // Ensure you have sweetalert2 installed
import BlogDetail from './blogs/components/BlogDetail';
import CurrentAffairsQuestionsDashboard from './currentAffairsQuestion/normalQuestions/pages/CurrentAffairsQuestionsDashboard'
import AllBlogs from './blogs/pages/AllBlogs';
import BlogMasterOptionDashboard from './currentAffairsQuestion/masterOptions/pages/BlogMasterOptionDashboard';
import BlogMasterOptionAddQuestionDashboard from './currentAffairsQuestion/masterOptions/pages/BlogMasterOptionAddQuestionDashboard';
import BlogMasterQuestionDashboard from './currentAffairsQuestion/masterQuestions/pages/BlogMasterQuestionDashboard';
import BlogMasterQuestion from './currentAffairsQuestion/masterQuestions/components/BlogMasterQuestion';

import { initializeApp } from 'firebase/app';
import { messaging, onForegroundMessage } from './firebase';
import { getToken } from 'firebase/messaging';

export default function App() {
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastTitle, setToastTitle] = useState("");
  const [toastImage, setToastImage] = useState(""); // For image URL

  const requestNotificationPermission = async () => {
    const permission = await Notification.requestPermission();
    if (permission === "granted") {
      try {
        const token = await getToken(messaging, {
          vapidKey: import.meta.env.VITE_VAPID_KEY,
        });
        if (token) {
          console.log("FCM Token:", token);
          return token;
        }
      } catch (error) {
        console.error("Error getting FCM token:", error);
      }
    } else if (permission === "denied") {
      alert(
        "You denied the permission. Please grant it to use this app efficiently"
      );
    }
  };

  useEffect(() => {
    onForegroundMessage(messaging, (payload) => {
      console.log("Foreground message received:", payload);
      if (payload.notification) {
        setToastTitle(payload.notification.title);
        setToastMessage(payload.notification.body);
        setToastImage(payload.notification.image || './notification_logo.png');
        setShowToast(true);
      }
    });
  }, []);
  

  return (
    <>
      <ToastContainer position="top-center" className="p-3">
        {showToast && (
          <Toast
            style={{
              backgroundColor: '#fff',
              color: '#333',
              borderRadius: '8px',
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
              width: '100%',
            }}
          >
            <div>
              <div className="d-flex align-items-center justify-content-center">
                <div className='mr-2'>
                  <img
                    src="./notification_logo.png"
                    alt="shashtrarth"
                    className="rounded-circle"
                    style={{ marginLeft: "1em", width: '40px', height: '40px', objectFit: 'cover' }}
                  />
                </div>
                <div className='d-flex align-items-enter'>
                  <div className='mx-2'>
                    <p className='mt-2'>
                      <strong>{toastTitle}</strong>
                    </p>
                    <p className="mt-1">{toastMessage}</p>
                    <img
                      src={toastImage || './notification_logo.png'}
                      alt=""
                      style={{ marginLeft: "1em", width: '40px', height: '40px', objectFit: 'cover' }}
                    />
                  </div>
                  <div>
                    <button
                      onClick={() => setShowToast(false)}
                      className="btn text-warning"
                      style={{ margin: '10px' }}
                    >
                      Close
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </Toast>
        )}
      </ToastContainer>

      <BrowserRouter>
        <Routes>
          {/* <Route path="/" element={<Home />} /> */}
          {/* <Route path="/" element={<Navigate to="/contributor_login" replace />} /> */}
          <Route path="/" element={<ContributorLogin />} />
          <Route path="/contributor_signup" element={<ContributorSignupPage />} />
          <Route path="/contributor_login" element={<ContributorLogin />} />
          <Route path="/contributor_dashboard" element={<ContributorDashboard />} />
          <Route path="/view_courses" element={<CoursesDashboard />} />
          <Route path="/create_course" element={<CourseForm />} />
          <Route path="/all_course" element={<AllCourses />} />
          <Route path="/add_courses" element={<AddCourses />} />
          <Route path="/course/:courseSlug" element={<ViewCourse />} />
          <Route path="/test_patterns_dashboard" element={<AddTestPattern />} />
          <Route path="/view_test_patterns" element={<ViewTestPatterns />} />
          <Route path="/edit_test_pattern/:id" element={<EditTestPattern />} />
          <Route path="/view_questions" element={<ViewQuestions />} />
          <Route path="/subjects_dashboard" element={<CreateSubject />} />
          <Route path="/subject/:slug" element={<ViewSubjects />} />
          <Route path="/topic/:topic_slug" element={<ViewTopic />} />
          <Route path="/questions_dashboard" element={<QuestionsDashboard />} />
          <Route path="/master_questions_dashboard" element={<MasterQuestionDashboard />} />
          <Route path="/master_question/:slug" element={<MasterQuestion />} />
          <Route path="/master_options_dashboard" element={<MasterOptionDashboard />} />
          <Route path="/master_option_add_questions/:slug" element={<MasterOptionAddQuestionDashboard />} />
          <Route path="/blogs_dashboard" element={<BlogsDashboard />} />
          <Route path="/blogs" element={<Blogs />} />
          <Route path="/blog/:slug" element={<BlogDetail />} />
          <Route path="/previous_year_questions_dashboard" element={<PreviousYearQuestionsDashboard />} />
          <Route path="/all_blogs" element={<AllBlogs/>} />
          <Route path="/current_affairs_normal_questions/:slug" element={<CurrentAffairsQuestionsDashboard/>} />
          <Route path="/current_affairs_master_questions/:blogSlug" element={<BlogMasterQuestionDashboard/>} />
          <Route path="/blog_master_question/:blogSlug/:slug" element={<BlogMasterQuestion/>} />
          <Route path="/blog_master_option_add_questions/:blogSlug/:slug" element={<BlogMasterOptionAddQuestionDashboard />} />
          <Route path="/current_affairs_master_options/:blogSlug" element={<BlogMasterOptionDashboard/>} />
          <Route path="/contribution" element={<ContributionDashboard />} />
          <Route path="/add_tier/:subcourseSlug" element={<AddTier />} />
          <Route path="/add_paper/:tierSlug" element={<AddPaper />} />
          <Route path="/add_section/:paperSlug" element={<AddSection />} />
          <Route path="/add_module/:sectionSlug" element={<AddModule />} />
          <Route path="/shorts" element={<ShortMode />} />

        </Routes>
        <Toaster/>
      </BrowserRouter>
    </>
  );
}
