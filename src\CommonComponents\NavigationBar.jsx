import React, { useState } from "react";
import { Nav<PERSON>, Nav, Container, <PERSON><PERSON>, Modal } from "react-bootstrap";
import { <PERSON>aU<PERSON>, <PERSON>aB<PERSON>, FaExclamationCircle } from "react-icons/fa";
import { Link, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  logoutContributor,
  clearContributorState,
} from "../redux/slice/contributorSlice";
import { persistStore } from "redux-persist";
import { toast, Toaster } from "react-hot-toast";
import { persistor, store } from "../redux/store";
import Terms from "../lanndingPages/components/Terms";

const NavigationBar = () => {
  const [showInstructionModal, setShowInstructionModal] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const accessTokenContributor = useSelector(
    (state) => state.contributor?.accessToken
  );

  // Contributor Logout handler
  const handleLogoutContributor = async () => {
    try {
      await dispatch(logoutContributor());
      dispatch(clearContributorState());

      // Clear only Contributor persisted and normal state
      store.dispatch({ type: "LOGOUT_CONTRIBUTOR" });

      // Remove Contributor data from persisted storage
      await persistor.flush();
      await persistor.purge();

      toast.success("Contributor logged out successfully!");
      navigate("/");
    } catch (error) {
      toast.error("Contributor logout failed!");
    }
  };

  return (
    <Navbar expand="lg" bg="light" variant="light" sticky="top" className="shadow-sm">
      <Container>
        {/* Logo Section */}
        <Navbar.Brand as={Link} to="/">
          <img
            src="logoB.png"
            alt="Logo"
            width="180"
            height="auto"
            className="d-inline-block align-top"
          />
        </Navbar.Brand>

        {/* Responsive Toggle */}
        <Navbar.Toggle aria-controls="navbar-nav" />

        <Navbar.Collapse id="navbar-nav">
          <Nav className="ms-auto align-items-start align-items-md-center">
            {/* Contributor Section */}
            {accessTokenContributor ? (
              <>
                <Nav.Link
                  as={Link}
                  to="/contributor_dashboard"
                  className="text-success fw-bold"
                  style={{ fontSize: "0.9rem" }}
                >
                  Dashboard
                </Nav.Link>
                <Nav.Link
                  as={Link}
                  to="/contribution"
                  className="text-success fw-bold"
                  style={{ fontSize: "0.9rem" }}
                >
                  Contribution
                </Nav.Link>
              </>
            ) : null}

            {/* Contributor Logout Button */}
            {accessTokenContributor && (
              <Button
                variant="outline-danger"
                className="m-1"
                onClick={handleLogoutContributor}
              >
                <FaUser className="m-1" />
                Contributor Logout
              </Button>
            )}

            <Button
              as="a"
              href="https://exam.shashtrath.com"
              target="_blank"
              rel="noopener noreferrer"
              variant="outline-success"
              className="m-1 d-flex align-items-center gap-1"
            >
              <FaBook size={16} />
              Exam
            </Button>
            <Button
              variant="outline-primary"
              className="m-1 d-flex align-items-center gap-1"
              onClick={() => setShowInstructionModal(!showInstructionModal)}
            >
              <FaExclamationCircle size={16} />
              Instruction
            </Button>
          </Nav>
        </Navbar.Collapse>
      </Container>
      <Modal show={showInstructionModal} onHide={() => setShowInstructionModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Instructions</Modal.Title>
        </Modal.Header>
        <Modal.Body className="overflow-y-auto m-0 p-0">
          <Terms />
        </Modal.Body>
      </Modal>
      <Toaster />
    </Navbar>
  );
};

export default NavigationBar;
