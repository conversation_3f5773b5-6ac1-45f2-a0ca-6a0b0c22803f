import React from "react";
import { Mo<PERSON>, <PERSON>, Button } from "react-bootstrap";
import RichTextEditor from "./RichTextEditor";

const MasterQuestionPassageModal = ({
  show,
  handleClose,
  updatedData,
  handleInputChange,
  handleSubmit,
}) => {
  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Question</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group controlId="formTitle">
            <RichTextEditor
              label="Title"
              name="title"
              value={updatedData.title}
              onChange={handleInputChange}
              placeholder="Enter title"
              rows={2}
              required
            />
          </Form.Group>
          <Form.Group controlId="formPassageContent" className="mt-3">
            <RichTextEditor
              label="Passage Content"
              name="passage_content"
              value={updatedData.passage_content}
              onChange={handleInputChange}
              placeholder="Enter passage content"
              rows={4}
              required
            />
          </Form.Group>
          <Button variant="success" type="submit" className="mt-3">
            Save Changes
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default MasterQuestionPassageModal;
