import { <PERSON>, But<PERSON>, Accordion } from "react-bootstrap";
import { Link } from "react-router-dom";
import {
  IoEllipsisHorizontalCircleSharp,
  IoFilterCircle,
} from "react-icons/io5";
import {
  FaPlus,
  FaEdit,
  FaTrashAlt,
  FaExclamationTriangle,
  FaQuestionCircle,
  FaRegCircle,
  FaInfoCircle,
} from "react-icons/fa";
import QuestionRejectReasonModal from "./QuestionRejectReasonModal";
import { useState } from "react";
import MathTextRenderer from "./MathTextRenderer";

const MasterOptionCard = ({
  option,
  handleEdit,
  handleDelete,
  handleEditQuestion,
  handleDeleteQuestion,
  handleEditOption,
  handleDeleteOption,
}) => {
  const [showReasonModal, setShowReasonModal] = useState(false);

  const handleShowReason = () => setShowReasonModal(true);
  const handleCloseReason = () => setShowReasonModal(false);
  return (
    <>
      <Card
        key={option.slug}
        className="shadow position-relative"
        style={{
          width: "100%",
          marginBottom: "1rem",
          backgroundColor:
            option.approval_status === "approved"
              ? "#e6ffee"
              : option.approval_status === "rejected"
              ? "#ffe6e6"
              : "#ffffb3",
        }}
      >
        <Card.Body>
          {/* Option Details */}
          <div>
            <Card.Title>
              <div className="d-flex justify-content-between">
                <div>
                  <IoEllipsisHorizontalCircleSharp className="fs-6" />{" "}
                  <MathTextRenderer text={option.title} />
                </div>
                <div className="d-flex align-items-center">
                  <Link to={`/master_option_add_questions/${option.slug}`}>
                    <Button
                      variant="outline-success"
                      className="action-buttons m-1"
                    >
                      <FaPlus size={15} />
                    </Button>
                  </Link>
                  <Button
                    variant="outline-primary"
                    className="action-buttons m-1"
                    onClick={() => handleEdit(option)}
                  >
                    <FaEdit size={15} />
                  </Button>
                  <Button
                    variant="outline-danger"
                    className="action-buttons m-1"
                    onClick={() => handleDelete(option.slug)}
                  >
                    <FaTrashAlt size={15} />
                  </Button>
                </div>
              </div>
            </Card.Title>
            {/* Add rejection reason link */}
            {option?.approval_status === "rejected" && (
              <Button
                variant="link"
                className="text-danger p-0 mb-2"
                onClick={handleShowReason}
              >
                <FaInfoCircle className="me-1" />
                See why it rejected
              </Button>
            )}
            <Card.Text style={{ marginRight: "0.7rem", textAlign: "justify" }}>
              <IoFilterCircle className="fs-6" /> <MathTextRenderer text={option.option_content} />
            </Card.Text>
            <br />
            <Card.Text>
              <strong>
                <FaExclamationTriangle className="fs-6" /> Conditions:
              </strong>{" "}
              <MathTextRenderer text={option.conditions} />
            </Card.Text>
          </div>

          {/* Related Questions */}
          {option.related_questions.length > 0 ? (
            <div className="mt-2">
              {option.related_questions.map((question) => (
                <div
                  key={question.question_id}
                  style={{
                    backgroundColor:
                      question.approval_status === "approved"
                        ? "#d4edda"
                        : question.approval_status === "pending"
                        ? "#fff"
                        : "#f8d7da",
                    padding: "10px",
                    borderRadius: "5px",
                    marginBottom: "10px",
                  }}
                >
                  <Card.Title>
                    <span style={{ fontSize: "0.7rem" }}>
                      Subject: {question.subject_name}, Topic:{" "}
                      {question.topic_name}, Sub Topic:{" "}
                      {question.sub_topic_name}
                    </span>
                  </Card.Title>
                  <Card.Text
                    style={{ marginRight: "0.7rem", textAlign: "justify" }}
                  >
                    <FaQuestionCircle /> <MathTextRenderer text={question.content} />
                    <Button
                      variant="outline-success"
                      className="action-buttons m-1"
                      onClick={() => handleEditQuestion(question)}
                    >
                      <FaEdit size={15} />
                    </Button>
                    <Button
                      variant="outline-danger"
                      className="action-buttons m-1"
                      onClick={() => handleDeleteQuestion(question.slug)}
                    >
                      <FaTrashAlt size={15} />
                    </Button>
                  </Card.Text>

                  {question.attachments && (
                    <Card.Img
                      variant="top"
                      src={`${import.meta.env.VITE_BASE_URL}/${
                        question.attachments
                      }`}
                      className="img-fluid rounded-3 mb-4"
                    />
                  )}

                  {/* Explanation Accordion */}
                  {question.explanation && (
                    <Accordion className="mb-3">
                      <Accordion.Item eventKey="0">
                        <Accordion.Header>Explanation</Accordion.Header>
                        <Accordion.Body>
                          <div className="explanation-text">
                            <MathTextRenderer text={question.explanation} />
                          </div>
                          {/* Explanation Image - on next line */}
                          {question.explanation_attachment && (
                            <div className="explanation-image mt-3">
                              <img
                                src={`${import.meta.env.VITE_BASE_URL}/${question.explanation_attachment}`}
                                alt="Explanation attachment"
                                className="img-fluid rounded-3 shadow-sm"
                                style={{
                                  maxWidth: "100%",
                                  height: "auto",
                                  border: "1px solid #e9ecef"
                                }}
                              />
                            </div>
                          )}
                        </Accordion.Body>
                      </Accordion.Item>
                    </Accordion>
                  )}

                  {/* Options under the question */}
                  {question.options && question.options.length > 0 ? (
                    <ol
                      style={{ listStyleType: "decimal", padding: "0 1.5rem" }}
                    >
                      {question.options.map((opt) => (
                        <li
                          key={opt.slug}
                          style={{
                            marginBottom: "8px",
                            display: "flex",
                            alignItems: "flex-start",
                            gap: "8px",
                            color: opt.is_correct ? "#198754" : "#dc3545",
                            fontWeight: "bold",
                            padding: "4px 0",
                          }}
                        >
                          <FaRegCircle style={{ marginTop: "4px", flexShrink: 0 }} />
                          <div style={{ flex: 1, minWidth: 0 }}>
                            <div className="option-text">
                              <MathTextRenderer text={opt.option_text} />
                            </div>
                            {/* Option Image - on next line */}
                            {opt.attachments && (
                              <div className="option-image mt-3">
                                <img
                                  src={`${import.meta.env.VITE_BASE_URL}/${opt.attachments}`}
                                  alt="Option attachment"
                                  className="img-fluid rounded-3 shadow-sm"
                                  style={{
                                    maxWidth: "250px",
                                    height: "auto",
                                    border: "1px solid #e9ecef"
                                  }}
                                />
                              </div>
                            )}
                          </div>
                          <div style={{ display: "flex", gap: "4px", flexShrink: 0 }}>
                            <Button
                              variant="outline-primary"
                              size="sm"
                              onClick={() => handleEditOption(opt, question.slug)}
                              title="Edit Option"
                            >
                              <FaEdit size={12} />
                            </Button>
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() =>
                                handleDeleteOption(opt.slug, question.slug)
                              }
                              title="Delete Option"
                            >
                              <FaTrashAlt size={12} />
                            </Button>
                          </div>
                        </li>
                      ))}
                    </ol>
                  ) : (
                    <p className="text-info">
                      No options available for this question.
                    </p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted mt-3">
              No related questions available for this option.
            </p>
          )}
        </Card.Body>
      </Card>

      <QuestionRejectReasonModal
        show={showReasonModal}
        onHide={handleCloseReason}
        reason={option?.reason}
        reasonDocument={option?.reason_document}
      />
    </>
  );
};

export default MasterOptionCard;
