import React from "react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";

const ViewModal = ({ show, onHide, content }) => {
  if (!content) return null;

  return (
    <Modal show={show} onHide={onHide} centered lg>
      <Modal.Header closeButton>
        <Modal.Title>{content.name}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p><strong>Description:</strong> {content.description}</p>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>Close</Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ViewModal;
