import React from 'react'
import { Card, Col } from 'react-bootstrap'
import Skeleton from 'react-loading-skeleton'

const ContributionTileSkeleton = ({number, cardHeightPercentage = 100}) => {
  return (
   <>
   {
    [...Array(number)].map((_, index) => (
            <Col key={index}>
              <Card
                style={{
                  backgroundColor: "#eef9ee",
                  borderRadius: "10px",
                  color: "#fff",
                  height: `${cardHeightPercentage}%`,
                }}
              >
                <Card.Body className="text-center">
                  <Skeleton
                    circle
                    height={cardHeightPercentage === 100 ? 50 : 25}
                    width={cardHeightPercentage === 100 ? 50 : 25}
                    baseColor="#e6ffe6"
                    highlightColor="#c4f7c4"
                    className="mb-2"
                  />
                  <Skeleton
                    width="60%"
                    height={cardHeightPercentage === 100 ? 20 : 10}
                    baseColor="#e6ffe6"
                    highlightColor="#c4f7c4"
                  />
                  <Skeleton
                    width="40%"
                    height={cardHeightPercentage === 100 ? 20 : 10}
                    baseColor="#e6ffe6"
                    highlightColor="#c4f7c4"
                    className="mt-2"
                  />
                </Card.Body>
              </Card>
            </Col>
          ))
   }
   </>
  )
}

export default ContributionTileSkeleton
