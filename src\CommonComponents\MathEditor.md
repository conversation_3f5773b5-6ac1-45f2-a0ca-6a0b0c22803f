# Math Editor Components

A complete mathematical expression editor built with React, MathLive, and KaTeX for creating and previewing mathematical content.

## Components

### 1. MathInput
A WYSIWYG mathematical input field with virtual keyboard support.

**Props:**
- `value` (string): Current LaTeX value
- `onChange` (function): Callback when value changes
- `placeholder` (string): Placeholder text
- `disabled` (boolean): Whether input is disabled
- `showVirtualKeyboard` (boolean): Show/hide virtual keyboard

### 2. MathPreview
Renders LaTeX expressions using KaTeX.

**Props:**
- `latex` (string): LaTeX expression to render
- `displayMode` (boolean): Block vs inline math
- `showRaw` (boolean): Show raw LaTeX code
- `placeholder` (string): Text when no content

### 3. MathEditor
Complete editor combining input and preview.

**Props:**
- `value` (string): Current LaTeX value
- `onChange` (function): Callback when value changes
- `label` (string): Editor label
- `showPreview` (boolean): Show preview panel
- `showRawLatex` (boolean): Show raw LaTeX
- `displayMode` (boolean): Block vs inline math
- `showSaveButton` (boolean): Show save button
- `onSave` (function): Save callback

## Usage Examples

### Basic Math Input
```jsx
import MathInput from './CommonComponents/MathInput';

function MyComponent() {
  const [latex, setLatex] = useState('');
  
  return (
    <MathInput
      value={latex}
      onChange={setLatex}
      placeholder="Enter math expression..."
    />
  );
}
```

### Complete Math Editor
```jsx
import MathEditor from './CommonComponents/MathEditor';

function QuestionForm() {
  const [mathContent, setMathContent] = useState('');
  
  return (
    <MathEditor
      value={mathContent}
      onChange={setMathContent}
      label="Mathematical Expression"
      showPreview={true}
      showRawLatex={true}
      displayMode={true}
    />
  );
}
```

### Math Preview Only
```jsx
import MathPreview from './CommonComponents/MathPreview';

function DisplayMath({ latex }) {
  return (
    <MathPreview
      latex={latex}
      displayMode={true}
      showRaw={false}
    />
  );
}
```

## Common LaTeX Expressions

### Basic Operations
- Fractions: `\frac{a}{b}`
- Exponents: `x^2` or `x^{2n}`
- Subscripts: `x_1` or `x_{i+1}`
- Square root: `\sqrt{x}` or `\sqrt[n]{x}`

### Calculus
- Integral: `\int_a^b f(x) dx`
- Derivative: `\frac{d}{dx}f(x)`
- Partial: `\frac{\partial f}{\partial x}`
- Limit: `\lim_{x \to \infty} f(x)`

### Algebra
- Sum: `\sum_{i=1}^n x_i`
- Product: `\prod_{i=1}^n x_i`
- Matrix: `\begin{pmatrix} a & b \\ c & d \end{pmatrix}`

### Greek Letters
- Alpha: `\alpha`, Beta: `\beta`, Gamma: `\gamma`
- Delta: `\delta`, Epsilon: `\epsilon`, Theta: `\theta`
- Lambda: `\lambda`, Mu: `\mu`, Pi: `\pi`

### Logic & Sets
- And: `\land`, Or: `\lor`, Not: `\neg`
- Implies: `\implies`, Equivalent: `\iff`
- In: `\in`, Subset: `\subset`, Union: `\cup`
- Intersection: `\cap`, Empty set: `\emptyset`

## Virtual Keyboard

The math input includes a virtual keyboard with:
- Basic operations (+, -, ×, ÷)
- Fractions and exponents
- Greek letters
- Calculus symbols
- Logic symbols
- Matrices and vectors

## Integration with Forms

The math editor is integrated into:
- Question content (normal questions)
- Option content (all question types)
- Explanation fields

Math content is stored as LaTeX strings and can be:
- Saved to backend as `math_content` field
- Rendered in previews using KaTeX
- Edited with full WYSIWYG support

## Browser Support

- Chrome/Edge: Full support
- Firefox: Full support
- Safari: Full support
- Mobile browsers: Virtual keyboard may have limitations

## Dependencies

- `mathlive`: WYSIWYG math editor
- `react-katex`: React wrapper for KaTeX
- `katex`: Math rendering library
- `bootstrap`: UI components

## Troubleshooting

### Virtual Keyboard Not Showing
- Ensure `showVirtualKeyboard={true}` is set
- Check if MathLive is properly loaded
- Verify no CSS conflicts with z-index

### Math Not Rendering
- Check LaTeX syntax for errors
- Ensure KaTeX CSS is imported
- Verify react-katex is properly configured

### Performance Issues
- Use `displayMode={false}` for inline math
- Limit preview updates with debouncing
- Consider lazy loading for large forms
